<UserControl x:Class="BinanceOrderBookWPF.Views.TradeTickerView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:BinanceOrderBookWPF.Views"
             xmlns:converters="clr-namespace:BinanceOrderBookWPF"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="400">
    
    <UserControl.Resources>
        <!-- 引入转换器 -->
        <converters:BoolToColorConverter x:Key="BoolToColorConverter"/>

        <!-- Binance官方颜色 -->
        <SolidColorBrush x:Key="BuyBrush" Color="#0ECB81"/>
        <SolidColorBrush x:Key="SellBrush" Color="#F84960"/>
        <SolidColorBrush x:Key="BackgroundBrush" Color="#1E2329"/>
        <SolidColorBrush x:Key="BorderBrush" Color="#3C4043"/>
        <SolidColorBrush x:Key="TextBrush" Color="#EAECEF"/>
        
        <!-- 交易记录项样式 -->
        <Style x:Key="TradeItemStyle" TargetType="Border">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="6,1"/>
            <Setter Property="Height" Value="18"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#2B3139"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        
        <!-- 价格文本样式 -->
        <Style x:Key="PriceTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="BinancePlex, Microsoft YaHei, Consolas, Monaco, 'Courier New', monospace"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Normal"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="4,1"/>
        </Style>
        
        <!-- 数量文本样式 -->
        <Style x:Key="QuantityTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="BinancePlex, Microsoft YaHei, Consolas, Monaco, 'Courier New', monospace"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#EAECEF"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="4,1"/>
        </Style>
        
        <!-- 时间文本样式 -->
        <Style x:Key="TimeTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="BinancePlex, Microsoft YaHei"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Foreground" Value="#848E9C"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
        </Style>
    </UserControl.Resources>
    
    <Grid Background="{StaticResource BackgroundBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#2B3139" BorderBrush="{StaticResource BorderBrush}"
                BorderThickness="0,0,0,1" Height="24">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="70"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="价格 (USDT)"
                          FontFamily="BinancePlex, Microsoft YaHei"
                          FontSize="11" FontWeight="Normal"
                          Foreground="#848E9C"
                          VerticalAlignment="Center"
                          HorizontalAlignment="Center"/>
                <TextBlock Grid.Column="1" Text="数量 (USDT)"
                          FontFamily="BinancePlex, Microsoft YaHei"
                          FontSize="11" FontWeight="Normal"
                          Foreground="#848E9C"
                          VerticalAlignment="Center"
                          HorizontalAlignment="Center"/>
                <TextBlock Grid.Column="2" Text="时间"
                          FontFamily="BinancePlex, Microsoft YaHei"
                          FontSize="11" FontWeight="Normal"
                          Foreground="#848E9C"
                          VerticalAlignment="Center"
                          HorizontalAlignment="Center"/>
            </Grid>
        </Border>
        
        <!-- 交易记录列表 -->
        <ScrollViewer Grid.Row="1" 
                      VerticalScrollBarVisibility="Auto" 
                      HorizontalScrollBarVisibility="Hidden"
                      Background="Transparent">
            <ItemsControl x:Name="TradesItemsControl" 
                         ItemsSource="{Binding RecentTrades}"
                         VirtualizingPanel.IsVirtualizing="True"
                         VirtualizingPanel.VirtualizationMode="Recycling">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <VirtualizingStackPanel/>
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Border Style="{StaticResource TradeItemStyle}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="70"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <!-- 价格 -->
                                <TextBlock Grid.Column="0"
                                          Text="{Binding Price, StringFormat=F5}"
                                          Style="{StaticResource PriceTextStyle}">
                                    <TextBlock.Foreground>
                                        <Binding Path="IsBuyerMaker" Converter="{StaticResource BoolToColorConverter}"/>
                                    </TextBlock.Foreground>
                                </TextBlock>

                                <!-- 数量 -->
                                <TextBlock Grid.Column="1"
                                          Text="{Binding Quantity, StringFormat=F5}"
                                          Style="{StaticResource QuantityTextStyle}"/>
                                
                                <!-- 时间 -->
                                <TextBlock Grid.Column="2" 
                                          Text="{Binding Time, StringFormat=HH:mm:ss}"
                                          Style="{StaticResource TimeTextStyle}"/>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>
    </Grid>
</UserControl>
