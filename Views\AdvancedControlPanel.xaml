<UserControl x:Class="BinanceOrderBookWPF.Views.AdvancedControlPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:BinanceOrderBookWPF.Views"
             mc:Ignorable="d" 
             d:DesignHeight="80" d:DesignWidth="800">
    
    <UserControl.Resources>
        <!-- 控制按钮样式 -->
        <Style x:Key="ControlButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Width" Value="100"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="Margin" Value="0,0,8,0"/>
            <Setter Property="FontSize" Value="11"/>
        </Style>
        
        <!-- 切换按钮样式 -->
        <Style x:Key="ToggleButtonStyle" TargetType="ToggleButton">
            <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
            <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="FontFamily" Value="{StaticResource UIFont}"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ToggleButton">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsChecked" Value="True">
                                <Setter Property="Background" Value="{StaticResource AccentColor}"/>
                                <Setter Property="BorderBrush" Value="{StaticResource AccentColor}"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FF333333"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 数值输入框样式 -->
        <Style x:Key="NumberInputStyle" TargetType="TextBox" BasedOn="{StaticResource ModernTextBoxStyle}">
            <Setter Property="Width" Value="80"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="FontFamily" Value="{StaticResource MonospaceFont}"/>
        </Style>
    </UserControl.Resources>
    
    <Border Background="#FF1E1E1E" CornerRadius="8" Padding="16,12">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <!-- 深度控制 -->
            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                <TextBlock Text="深度:" Style="{StaticResource LabelTextStyle}"
                          VerticalAlignment="Center" Margin="0,0,8,0"/>
                <Slider x:Name="DepthSlider" Value="{Binding Depth}" Minimum="5" Maximum="50" Width="120"
                        Style="{StaticResource ModernSliderStyle}"
                        VerticalAlignment="Center" TickFrequency="5" IsSnapToTickEnabled="True"/>
                <TextBox Text="{Binding Depth}" Style="{StaticResource NumberInputStyle}"
                         Margin="8,0,0,0"/>
            </StackPanel>
            
            <!-- 刷新率控制 -->
            <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="24,0,0,0">
                <TextBlock Text="刷新率:" Style="{StaticResource LabelTextStyle}"
                          VerticalAlignment="Center" Margin="0,0,8,0"/>
                <ComboBox x:Name="RefreshRateComboBox" Width="80" Height="32"
                          Style="{StaticResource ModernComboBoxStyle}">
                    <ComboBoxItem Content="100ms"/>
                    <ComboBoxItem Content="250ms" IsSelected="True"/>
                    <ComboBoxItem Content="500ms"/>
                    <ComboBoxItem Content="1000ms"/>
                </ComboBox>
            </StackPanel>
            
            <!-- 显示选项 -->
            <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center" Margin="24,0,0,0">
                <TextBlock Text="显示:" Style="{StaticResource LabelTextStyle}"
                          VerticalAlignment="Center" Margin="0,0,8,0"/>
                <ToggleButton x:Name="ShowDepthBarsToggle" Content="深度条" 
                             Style="{StaticResource ToggleButtonStyle}"
                             IsChecked="True" Margin="0,0,4,0"/>
                <ToggleButton x:Name="ShowAnimationsToggle" Content="动画" 
                             Style="{StaticResource ToggleButtonStyle}"
                             IsChecked="True" Margin="0,0,4,0"/>
                <ToggleButton x:Name="ShowSoundToggle" Content="音效" 
                             Style="{StaticResource ToggleButtonStyle}"
                             IsChecked="False"/>
            </StackPanel>
            
            <!-- 快捷操作 -->
            <StackPanel Grid.Column="3" Orientation="Horizontal" VerticalAlignment="Center" Margin="24,0,0,0">
                <Button x:Name="ResetViewButton" Content="重置视图" 
                       Style="{StaticResource ControlButtonStyle}"/>
                <Button x:Name="ExportDataButton" Content="导出数据" 
                       Style="{StaticResource ControlButtonStyle}"/>
                <Button x:Name="ScreenshotButton" Content="截图" 
                       Style="{StaticResource ControlButtonStyle}"/>
            </StackPanel>
            
            <!-- 状态信息 -->
            <StackPanel Grid.Column="5" Orientation="Horizontal" VerticalAlignment="Center">
                <TextBlock Text="延迟:" Style="{StaticResource LabelTextStyle}"
                          VerticalAlignment="Center" Margin="0,0,4,0"/>
                <TextBlock Text="{Binding Latency, StringFormat={}{0}ms}" 
                          Style="{StaticResource ValueTextStyle}"
                          Foreground="#FFFFFF99" VerticalAlignment="Center" Margin="0,0,16,0"/>
                
                <TextBlock Text="更新:" Style="{StaticResource LabelTextStyle}"
                          VerticalAlignment="Center" Margin="0,0,4,0"/>
                <TextBlock Text="{Binding UpdatesPerSecond, StringFormat={}{0}/s}" 
                          Style="{StaticResource ValueTextStyle}"
                          Foreground="#FF3B82F6" VerticalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Border>
</UserControl>
