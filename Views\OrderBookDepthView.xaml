<UserControl x:Class="BinanceOrderBookWPF.Views.OrderBookDepthView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:BinanceOrderBookWPF.Views"
             xmlns:converters="clr-namespace:BinanceOrderBookWPF"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="600">
    
    <UserControl.Resources>
        <!-- 引入转换器 -->
        <converters:DepthToWidthConverter x:Key="DepthToWidthConverter"/>

        <!-- 买单颜色 - RGB 46,189,133 -->
        <SolidColorBrush x:Key="BidBrush" Color="#FF2EBD85"/>
        <!-- 卖单颜色 - RGB 246,70,93 -->
        <SolidColorBrush x:Key="AskBrush" Color="#FFF6465D"/>
        <!-- 背景色 -->
        <SolidColorBrush x:Key="BackgroundBrush" Color="#FF1E1E1E"/>
        <!-- 边框色 -->
        <SolidColorBrush x:Key="BorderBrush" Color="#FF333333"/>
        <!-- 文字色 -->
        <SolidColorBrush x:Key="TextBrush" Color="#FFCCCCCC"/>
        
        <!-- 订单薄条目样式 -->
        <Style x:Key="OrderBookItemStyle" TargetType="Border">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="6,1"/>
            <Setter Property="Height" Value="18"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#2B3139"/>
                    <Trigger.EnterActions>
                        <BeginStoryboard>
                            <Storyboard>
                                <ColorAnimation Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                              To="#2B3139" Duration="0:0:0.1"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </Trigger.EnterActions>
                    <Trigger.ExitActions>
                        <BeginStoryboard>
                            <Storyboard>
                                <ColorAnimation Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                              To="Transparent" Duration="0:0:0.1"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </Trigger.ExitActions>
                </Trigger>
            </Style.Triggers>
        </Style>
        
        <!-- 价格文本样式 -->
        <Style x:Key="PriceTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="BinancePlex, Microsoft YaHei, Consolas, Monaco, 'Courier New', monospace"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Normal"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Left"/>
            <Setter Property="Margin" Value="4,1"/>
        </Style>

        <!-- 数量文本样式 -->
        <Style x:Key="QuantityTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="BinancePlex, Microsoft YaHei, Consolas, Monaco, 'Courier New', monospace"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#EAECEF"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Right"/>
            <Setter Property="Margin" Value="4,1"/>
        </Style>

        <!-- 累计文本样式 -->
        <Style x:Key="TotalTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="BinancePlex, Microsoft YaHei, Consolas, Monaco, 'Courier New', monospace"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Foreground" Value="#848E9C"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Right"/>
            <Setter Property="Margin" Value="4,1"/>
        </Style>
    </UserControl.Resources>
    
    <Grid Background="{StaticResource BackgroundBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- 卖单标题 -->
        <Border Grid.Row="0" Background="#FF2A2A2A" BorderBrush="{StaticResource BorderBrush}" 
                BorderThickness="0,0,0,1" Height="24">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="价格 (USDT)"
                          Foreground="#848E9C" FontWeight="Normal" FontSize="11"
                          FontFamily="BinancePlex, Microsoft YaHei"
                          VerticalAlignment="Center" HorizontalAlignment="Left" Margin="8,0"/>
                <TextBlock Grid.Column="1" Text="数量 (USDT)"
                          Foreground="#848E9C" FontWeight="Normal" FontSize="11"
                          FontFamily="BinancePlex, Microsoft YaHei"
                          VerticalAlignment="Center" HorizontalAlignment="Right" Margin="4,0"/>
                <TextBlock Grid.Column="2" Text="合计 (USDT)"
                          Foreground="#848E9C" FontWeight="Normal" FontSize="11"
                          FontFamily="BinancePlex, Microsoft YaHei"
                          VerticalAlignment="Center" HorizontalAlignment="Right" Margin="8,0"/>
            </Grid>
        </Border>
        
        <!-- 卖单列表 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Hidden" HorizontalScrollBarVisibility="Hidden">
            <ItemsControl ItemsSource="{Binding Asks}" 
                         VirtualizingPanel.IsVirtualizing="True"
                         VirtualizingPanel.VirtualizationMode="Recycling">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <VirtualizingStackPanel/>
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Border Style="{StaticResource OrderBookItemStyle}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="100"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <!-- 背景深度条 -->
                                <Rectangle Grid.ColumnSpan="3"
                                          Fill="#F84960"
                                          Opacity="0.08"
                                          HorizontalAlignment="Right"
                                          Width="{Binding RelativeDepth, Converter={StaticResource DepthToWidthConverter}}"/>
                                
                                <!-- 价格 -->
                                <TextBlock Grid.Column="0" 
                                          Text="{Binding Price, StringFormat=F2}"
                                          Style="{StaticResource PriceTextStyle}"
                                          Foreground="{StaticResource AskBrush}"/>
                                
                                <!-- 数量 -->
                                <TextBlock Grid.Column="1" 
                                          Text="{Binding Quantity, StringFormat=F4}"
                                          Style="{StaticResource QuantityTextStyle}"/>
                                
                                <!-- 累计 -->
                                <TextBlock Grid.Column="2" 
                                          Text="{Binding Total, StringFormat=F2}"
                                          Style="{StaticResource TotalTextStyle}"/>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>
        
        <!-- 价差显示 -->
        <Border Grid.Row="2" Background="#FF2A2A2A" Height="32" Margin="0,2">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="{Binding HighestBid, StringFormat=F5}"
                          Foreground="#0ECB81" FontFamily="BinancePlex, Microsoft YaHei, Consolas"
                          FontSize="13" FontWeight="Medium"
                          HorizontalAlignment="Right" VerticalAlignment="Center" Margin="0,0,8,0"/>

                <TextBlock Grid.Column="1" Text="↓ 0.00001"
                          Foreground="#F84960" FontSize="11" FontWeight="Normal"
                          FontFamily="BinancePlex, Microsoft YaHei"
                          HorizontalAlignment="Center" VerticalAlignment="Center"/>

                <TextBlock Grid.Column="2" Text="{Binding LowestAsk, StringFormat=F5}"
                          Foreground="#F84960" FontFamily="BinancePlex, Microsoft YaHei, Consolas"
                          FontSize="13" FontWeight="Medium"
                          HorizontalAlignment="Left" VerticalAlignment="Center" Margin="8,0,0,0"/>
            </Grid>
        </Border>
        
        <!-- 买单列表 -->
        <ScrollViewer Grid.Row="3" VerticalScrollBarVisibility="Hidden" HorizontalScrollBarVisibility="Hidden">
            <ItemsControl ItemsSource="{Binding Bids}" 
                         VirtualizingPanel.IsVirtualizing="True"
                         VirtualizingPanel.VirtualizationMode="Recycling">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <VirtualizingStackPanel/>
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Border Style="{StaticResource OrderBookItemStyle}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="100"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <!-- 背景深度条 -->
                                <Rectangle Grid.ColumnSpan="3"
                                          Fill="#0ECB81"
                                          Opacity="0.08"
                                          HorizontalAlignment="Right"
                                          Width="{Binding RelativeDepth, Converter={StaticResource DepthToWidthConverter}}"/>
                                
                                <!-- 价格 -->
                                <TextBlock Grid.Column="0" 
                                          Text="{Binding Price, StringFormat=F2}"
                                          Style="{StaticResource PriceTextStyle}"
                                          Foreground="{StaticResource BidBrush}"/>
                                
                                <!-- 数量 -->
                                <TextBlock Grid.Column="1" 
                                          Text="{Binding Quantity, StringFormat=F4}"
                                          Style="{StaticResource QuantityTextStyle}"/>
                                
                                <!-- 累计 -->
                                <TextBlock Grid.Column="2" 
                                          Text="{Binding Total, StringFormat=F2}"
                                          Style="{StaticResource TotalTextStyle}"/>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>
    </Grid>
</UserControl>
