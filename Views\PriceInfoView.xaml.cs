using System.Windows.Controls;
using System.Windows.Media;

namespace BinanceOrderBookWPF.Views
{
    /// <summary>
    /// PriceInfoView.xaml 的交互逻辑
    /// </summary>
    public partial class PriceInfoView : UserControl
    {
        public PriceInfoView()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 更新连接状态
        /// </summary>
        /// <param name="isConnected">是否已连接</param>
        public void UpdateConnectionStatus(bool isConnected)
        {
            ConnectionIndicator.Fill = isConnected ?
                new SolidColorBrush(Colors.Green) :
                new SolidColorBrush(Colors.Red);

            ConnectionStatusText.Text = isConnected ? "已连接" : "重连中...";
            ConnectionStatusText.Foreground = isConnected ?
                new SolidColorBrush(Colors.Green) :
                new SolidColorBrush(Colors.Red);
        }
    }
}
