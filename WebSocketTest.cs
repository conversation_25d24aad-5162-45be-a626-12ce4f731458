using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using BinanceOrderBookWPF.Services;

namespace BinanceOrderBookWPF
{
    public class WebSocketTest
    {
        public static async Task TestWebSocketConnection()
        {
            // 配置依赖注入
            var services = new ServiceCollection();
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Debug);
            });
            services.AddSingleton<BinanceWebSocketService>();

            var serviceProvider = services.BuildServiceProvider();
            var webSocketService = serviceProvider.GetRequiredService<BinanceWebSocketService>();
            var logger = serviceProvider.GetRequiredService<ILogger<WebSocketTest>>();

            // 订阅事件
            webSocketService.OrderBookSnapshotReceived += snapshot =>
            {
                logger.LogInformation($"收到订单薄快照: LastUpdateId={snapshot.LastUpdateId}, Bids={snapshot.Bids.Count}, Asks={snapshot.Asks.Count}");
            };

            webSocketService.OrderBookUpdateReceived += update =>
            {
                logger.LogInformation($"收到订单薄更新: UpdateId={update.FinalUpdateId}, Bids={update.Bids.Count}, Asks={update.Asks.Count}");
            };

            webSocketService.AggTradeReceived += trade =>
            {
                logger.LogInformation($"收到交易数据: Price={trade.Price}, Quantity={trade.Quantity}");
            };

            webSocketService.ConnectionStatusChanged += isConnected =>
            {
                logger.LogInformation($"连接状态变化: {(isConnected ? "已连接" : "已断开")}");
            };

            webSocketService.ErrorOccurred += error =>
            {
                logger.LogError($"WebSocket错误: {error}");
            };

            try
            {
                logger.LogInformation("开始测试WebSocket连接...");
                await webSocketService.ConnectAsync("ETHUSDT");
                
                logger.LogInformation("等待30秒接收数据...");
                await Task.Delay(30000);
                
                logger.LogInformation($"连接状态: {webSocketService.IsConnected}");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "测试过程中发生错误");
            }
            finally
            {
                await webSocketService.DisconnectAsync();
                webSocketService.Dispose();
            }
        }
    }
}
