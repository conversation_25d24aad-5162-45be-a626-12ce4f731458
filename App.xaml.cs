﻿using System.Configuration;
using System.Data;
using System.Windows;
using System.Threading.Tasks;

namespace BinanceOrderBookWPF;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    protected override async void OnStartup(StartupEventArgs e)
    {
        // 如果命令行参数包含 "test"，运行测试而不是启动UI
        if (e.Args.Length > 0 && e.Args[0] == "test")
        {
            await WebSocketTest.TestWebSocketConnection();
            Shutdown();
            return;
        }

        base.OnStartup(e);
    }
}

