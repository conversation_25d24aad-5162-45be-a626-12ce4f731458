{"format": 1, "restore": {"E:\\BinanceOrderBookWPF\\BinanceOrderBookWPF.csproj": {}}, "projects": {"E:\\BinanceOrderBookWPF\\BinanceOrderBookWPF.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\BinanceOrderBookWPF\\BinanceOrderBookWPF.csproj", "projectName": "BinanceOrderBookWPF", "projectPath": "E:\\BinanceOrderBookWPF\\BinanceOrderBookWPF.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\BinanceOrderBookWPF\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[9.0.7, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "ScottPlot.WPF": {"target": "Package", "version": "[5.0.55, )"}, "System.Net.WebSockets.Client": {"target": "Package", "version": "[4.3.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}