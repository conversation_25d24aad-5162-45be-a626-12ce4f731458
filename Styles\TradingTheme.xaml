<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <!-- 颜色定义 - 基于Binance官方设计 -->
    <SolidColorBrush x:Key="BuyColor" Color="#0ECB81"/>      <!-- Binance绿色 -->
    <SolidColorBrush x:Key="SellColor" Color="#F84960"/>     <!-- Binance红色 -->
    <SolidColorBrush x:Key="BackgroundColor" Color="#1E2329"/>   <!-- Binance背景色 -->
    <SolidColorBrush x:Key="SurfaceColor" Color="#2B3139"/>      <!-- Binance表面色 -->
    <SolidColorBrush x:Key="BorderColor" Color="#3C4043"/>       <!-- 边框色 -->
    <SolidColorBrush x:Key="TextPrimaryColor" Color="#EAECEF"/>  <!-- 主文字色 -->
    <SolidColorBrush x:Key="TextSecondaryColor" Color="#B7BDC6"/><!-- 次要文字色 -->
    <SolidColorBrush x:Key="TextMutedColor" Color="#848E9C"/>    <!-- 静音文字色 -->
    <SolidColorBrush x:Key="AccentColor" Color="#FCD535"/>       <!-- Binance黄色强调 -->
    <SolidColorBrush x:Key="WarningColor" Color="#F0B90B"/>      <!-- 警告色 -->
    
    <!-- 字体定义 -->
    <FontFamily x:Key="MonospaceFont">BinancePlex, Microsoft YaHei, Consolas, Monaco, 'Courier New', monospace</FontFamily>
    <FontFamily x:Key="UIFont">BinancePlex, Microsoft YaHei, Segoe UI, Arial, sans-serif</FontFamily>
    
    <!-- 按钮样式 -->
    <Style x:Key="ModernButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource AccentColor}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="FontFamily" Value="{StaticResource UIFont}"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            CornerRadius="6"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center" 
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#FF4F94FF"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#FF2563EB"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 文本框样式 -->
    <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
        <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="FontFamily" Value="{StaticResource UIFont}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="6">
                        <ScrollViewer x:Name="PART_ContentHost" 
                                    Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter Property="BorderBrush" Value="{StaticResource AccentColor}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 下拉框样式 -->
    <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox">
        <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="FontFamily" Value="{StaticResource UIFont}"/>
    </Style>
    
    <!-- 滑块样式 -->
    <Style x:Key="ModernSliderStyle" TargetType="Slider">
        <Setter Property="Background" Value="{StaticResource BorderColor}"/>
        <Setter Property="Foreground" Value="{StaticResource AccentColor}"/>
        <Setter Property="Height" Value="20"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Slider">
                    <Grid>
                        <Border Background="{TemplateBinding Background}"
                                Height="4"
                                CornerRadius="2"
                                VerticalAlignment="Center"/>
                        <Track x:Name="PART_Track">
                            <Track.DecreaseRepeatButton>
                                <RepeatButton Background="{TemplateBinding Foreground}"
                                            Height="4"
                                            Template="{x:Null}"/>
                            </Track.DecreaseRepeatButton>
                            <Track.Thumb>
                                <Thumb Background="{TemplateBinding Foreground}"
                                     Width="16"
                                     Height="16"
                                     Template="{DynamicResource SliderThumbTemplate}"/>
                            </Track.Thumb>
                            <Track.IncreaseRepeatButton>
                                <RepeatButton Background="Transparent"
                                            Template="{x:Null}"/>
                            </Track.IncreaseRepeatButton>
                        </Track>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 滑块拖拽按钮模板 -->
    <ControlTemplate x:Key="SliderThumbTemplate" TargetType="Thumb">
        <Ellipse Fill="{TemplateBinding Background}"
                 Stroke="{StaticResource TextPrimaryColor}"
                 StrokeThickness="2"/>
    </ControlTemplate>
    
    <!-- 卡片样式 -->
    <Style x:Key="CardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" BlurRadius="8" ShadowDepth="2" Opacity="0.3"/>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#FF333333"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="Black" BlurRadius="12" ShadowDepth="3" Opacity="0.4"/>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 增强的价格卡片样式 -->
    <Style x:Key="PriceCardEnhancedStyle" TargetType="Border">
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <GradientStop Color="#FF2A2A2A" Offset="0"/>
                    <GradientStop Color="#FF252525" Offset="1"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="Margin" Value="0,0,12,0"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" BlurRadius="6" ShadowDepth="2" Opacity="0.25"/>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background">
                    <Setter.Value>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                            <GradientStop Color="#FF333333" Offset="0"/>
                            <GradientStop Color="#FF2E2E2E" Offset="1"/>
                        </LinearGradientBrush>
                    </Setter.Value>
                </Setter>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="Black" BlurRadius="10" ShadowDepth="3" Opacity="0.35"/>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>
    
    <!-- 标题文本样式 -->
    <Style x:Key="TitleTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource UIFont}"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
    </Style>

    <!-- 副标题文本样式 -->
    <Style x:Key="SubtitleTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource UIFont}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryColor}"/>
    </Style>

    <!-- 标签文本样式 -->
    <Style x:Key="LabelTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource UIFont}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Foreground" Value="{StaticResource TextMutedColor}"/>
    </Style>

    <!-- 数值文本样式 -->
    <Style x:Key="ValueTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource MonospaceFont}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
    </Style>

    <!-- 价格文本样式 -->
    <Style x:Key="PriceTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource MonospaceFont}"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
    </Style>

    <!-- 动画价格文本样式 -->
    <Style x:Key="AnimatedPriceTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource MonospaceFont}"/>
        <Setter Property="FontSize" Value="20"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Style.Triggers>
            <EventTrigger RoutedEvent="Binding.TargetUpdated">
                <BeginStoryboard>
                    <Storyboard>
                        <ColorAnimation Storyboard.TargetProperty="(TextBlock.Foreground).(SolidColorBrush.Color)"
                                      To="#FFFFFF99" Duration="0:0:0.2"/>
                        <ColorAnimation Storyboard.TargetProperty="(TextBlock.Foreground).(SolidColorBrush.Color)"
                                      To="White" Duration="0:0:0.3" BeginTime="0:0:0.2"/>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
        </Style.Triggers>
    </Style>

    <!-- 脉冲动画样式 -->
    <Style x:Key="PulseAnimationStyle" TargetType="Border">
        <Style.Triggers>
            <EventTrigger RoutedEvent="Loaded">
                <BeginStoryboard>
                    <Storyboard RepeatBehavior="Forever">
                        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                       From="1.0" To="0.6" Duration="0:0:1"
                                       AutoReverse="True"/>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
        </Style.Triggers>
    </Style>

    <!-- 悬停放大效果 -->
    <Style x:Key="HoverScaleStyle" TargetType="Border">
        <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <ScaleTransform ScaleX="1" ScaleY="1"/>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Trigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleX"
                                           To="1.02" Duration="0:0:0.1"/>
                            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleY"
                                           To="1.02" Duration="0:0:0.1"/>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.EnterActions>
                <Trigger.ExitActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleX"
                                           To="1" Duration="0:0:0.1"/>
                            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleY"
                                           To="1" Duration="0:0:0.1"/>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.ExitActions>
            </Trigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>
