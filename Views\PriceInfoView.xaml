<UserControl x:Class="BinanceOrderBookWPF.Views.PriceInfoView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:BinanceOrderBookWPF.Views"
             xmlns:converters="clr-namespace:BinanceOrderBookWPF"
             mc:Ignorable="d" 
             d:DesignHeight="80" d:DesignWidth="800">
    
    <UserControl.Resources>
        <converters:PriceChangeToColorConverter x:Key="PriceChangeToColorConverter"/>
        
        <!-- 使用增强的价格卡片样式 -->
        <Style x:Key="PriceCardStyle" TargetType="Border" BasedOn="{StaticResource PriceCardEnhancedStyle}">
            <!-- 继承增强样式，可以在这里添加特定的覆盖 -->
        </Style>
        
        <!-- 主价格文本样式 -->
        <Style x:Key="MainPriceStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="BinancePlex, Microsoft YaHei, Consolas, Monaco, 'Courier New', monospace"/>
            <Setter Property="FontSize" Value="20"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#EAECEF"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- 变化文本样式 -->
        <Style x:Key="ChangeTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="BinancePlex, Microsoft YaHei, Consolas, Monaco, 'Courier New', monospace"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="Normal"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="8,0,0,0"/>
        </Style>

        <!-- 标签文本样式 -->
        <Style x:Key="LabelTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="BinancePlex, Microsoft YaHei"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Foreground" Value="#848E9C"/>
            <Setter Property="FontWeight" Value="Normal"/>
            <Setter Property="Margin" Value="0,0,0,2"/>
        </Style>

        <!-- 数值文本样式 -->
        <Style x:Key="ValueTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="BinancePlex, Microsoft YaHei, Consolas, Monaco, 'Courier New', monospace"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#EAECEF"/>
            <Setter Property="FontWeight" Value="Normal"/>
        </Style>
    </UserControl.Resources>
    
    <Border Background="#FF1E1E1E" CornerRadius="8" Padding="16,12">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- 主价格显示 -->
            <Border Grid.Column="0" Style="{StaticResource PriceCardStyle}">
                <StackPanel>
                    <TextBlock Text="最新价格" Style="{StaticResource LabelTextStyle}"/>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="{Binding LastTrade.Price, StringFormat=F2, NotifyOnTargetUpdated=True}"
                                  Style="{StaticResource AnimatedPriceTextStyle}"/>
                        <StackPanel Orientation="Vertical" Margin="12,0,0,0">
                            <TextBlock Text="{Binding LastTrade.Change, StringFormat=+0.00;-0.00;0.00}"
                                      Style="{StaticResource ChangeTextStyle}"
                                      Foreground="{Binding LastTrade.Change, Converter={StaticResource PriceChangeToColorConverter}}"/>
                            <TextBlock Text="{Binding LastTrade.ChangePercent, StringFormat=({0:F2}%)}"
                                      Style="{StaticResource ChangeTextStyle}"
                                      Foreground="{Binding LastTrade.Change, Converter={StaticResource PriceChangeToColorConverter}}"/>
                        </StackPanel>
                    </StackPanel>
                </StackPanel>
            </Border>
            
            <!-- 最高买价 -->
            <Border Grid.Column="1" Style="{StaticResource PriceCardStyle}">
                <StackPanel>
                    <TextBlock Text="最高买价" Style="{StaticResource LabelTextStyle}"/>
                    <TextBlock Text="{Binding HighestBid, StringFormat=F2}"
                              Style="{StaticResource ValueTextStyle}"
                              Foreground="#FF2EBD85"/>
                </StackPanel>
            </Border>
            
            <!-- 最低卖价 -->
            <Border Grid.Column="2" Style="{StaticResource PriceCardStyle}">
                <StackPanel>
                    <TextBlock Text="最低卖价" Style="{StaticResource LabelTextStyle}"/>
                    <TextBlock Text="{Binding LowestAsk, StringFormat=F2}"
                              Style="{StaticResource ValueTextStyle}"
                              Foreground="#FFF6465D"/>
                </StackPanel>
            </Border>
            
            <!-- 价差 -->
            <Border Grid.Column="3" Style="{StaticResource PriceCardStyle}">
                <StackPanel>
                    <TextBlock Text="价差" Style="{StaticResource LabelTextStyle}"/>
                    <TextBlock Text="{Binding Spread, StringFormat=F2}" 
                              Style="{StaticResource ValueTextStyle}"
                              Foreground="#FFFFFF99"/>
                </StackPanel>
            </Border>
            
            <!-- 连接状态 -->
            <Border Grid.Column="4" Style="{StaticResource PulseAnimationStyle}"
                   HorizontalAlignment="Right" VerticalAlignment="Center"
                   Background="Transparent" Padding="8,4" CornerRadius="4">
                <StackPanel Orientation="Horizontal">
                    <Ellipse x:Name="ConnectionIndicator" Width="12" Height="12"
                            Fill="Green" Margin="0,0,8,0"
                            ToolTip="连接状态"/>
                    <TextBlock x:Name="ConnectionStatusText" Text="已连接"
                              Foreground="#0ECB81" FontSize="11" VerticalAlignment="Center"/>
                </StackPanel>
            </Border>
        </Grid>
    </Border>
</UserControl>
