using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using Newtonsoft.Json;

namespace BinanceOrderBookWPF.Models
{
    // 订单薄快照数据模型
    public class OrderBookSnapshot
    {
        [JsonProperty("lastUpdateId")]
        public long LastUpdateId { get; set; }

        [JsonProperty("bids")]
        public List<List<string>> Bids { get; set; } = new List<List<string>>();

        [JsonProperty("asks")]
        public List<List<string>> Asks { get; set; } = new List<List<string>>();
    }

    // 订单薄更新数据模型
    public class OrderBookUpdate
    {
        [JsonProperty("e")]
        public string EventType { get; set; } = string.Empty;

        [JsonProperty("E")]
        public long EventTime { get; set; }

        [JsonProperty("s")]
        public string Symbol { get; set; } = string.Empty;

        [JsonProperty("U")]
        public long FirstUpdateId { get; set; }

        [JsonProperty("u")]
        public long FinalUpdateId { get; set; }

        [JsonProperty("b")]
        public List<List<string>> Bids { get; set; } = new List<List<string>>();

        [JsonProperty("a")]
        public List<List<string>> Asks { get; set; } = new List<List<string>>();
    }

    // 聚合交易数据模型
    public class AggTradeUpdate
    {
        [JsonProperty("e")]
        public string EventType { get; set; } = string.Empty;

        [JsonProperty("E")]
        public long EventTime { get; set; }

        [JsonProperty("s")]
        public string Symbol { get; set; } = string.Empty;

        [JsonProperty("a")]
        public long AggregateTradeId { get; set; }

        [JsonProperty("p")]
        public string Price { get; set; } = string.Empty;

        [JsonProperty("q")]
        public string Quantity { get; set; } = string.Empty;

        [JsonProperty("f")]
        public long FirstTradeId { get; set; }

        [JsonProperty("l")]
        public long LastTradeId { get; set; }

        [JsonProperty("T")]
        public long TradeTime { get; set; }

        [JsonProperty("m")]
        public bool IsBuyerMaker { get; set; }
    }

    // 24小时价格统计数据模型
    public class MiniTickerUpdate
    {
        [JsonProperty("e")]
        public string EventType { get; set; } = string.Empty;

        [JsonProperty("E")]
        public long EventTime { get; set; }

        [JsonProperty("s")]
        public string Symbol { get; set; } = string.Empty;

        [JsonProperty("c")]
        public string ClosePrice { get; set; } = string.Empty;

        [JsonProperty("o")]
        public string OpenPrice { get; set; } = string.Empty;

        [JsonProperty("h")]
        public string HighPrice { get; set; } = string.Empty;

        [JsonProperty("l")]
        public string LowPrice { get; set; } = string.Empty;

        [JsonProperty("v")]
        public string Volume { get; set; } = string.Empty;

        [JsonProperty("q")]
        public string QuoteVolume { get; set; } = string.Empty;
    }

    // WebSocket消息包装器
    public class WebSocketMessage
    {
        [JsonProperty("stream")]
        public string Stream { get; set; } = string.Empty;

        [JsonProperty("data")]
        public object Data { get; set; } = new object();
    }

    // 订单薄条目
    public class OrderBookEntry : INotifyPropertyChanged
    {
        private double _price;
        private double _quantity;
        private double _total;
        private double _relativeDepth;

        public double Price
        {
            get => _price;
            set
            {
                _price = value;
                OnPropertyChanged();
            }
        }

        public double Quantity
        {
            get => _quantity;
            set
            {
                _quantity = value;
                OnPropertyChanged();
            }
        }

        public double Total
        {
            get => _total;
            set
            {
                _total = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 相对深度，用于显示深度条（0-1之间的值）
        /// </summary>
        public double RelativeDepth
        {
            get => _relativeDepth;
            set
            {
                _relativeDepth = value;
                OnPropertyChanged();
            }
        }

        public bool IsBid { get; set; }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    // 最新交易信息
    public class LastTrade : INotifyPropertyChanged
    {
        private double _price;
        private double _priceChange;
        private double _change;
        private double _changePercent;
        private string _symbol = string.Empty;

        public double Price
        {
            get => _price;
            set
            {
                _price = value;
                OnPropertyChanged();
            }
        }

        public double PriceChange
        {
            get => _priceChange;
            set
            {
                _priceChange = value;
                OnPropertyChanged();
            }
        }

        public double Change
        {
            get => _change;
            set
            {
                _change = value;
                OnPropertyChanged();
            }
        }

        public double ChangePercent
        {
            get => _changePercent;
            set
            {
                _changePercent = value;
                OnPropertyChanged();
            }
        }

        public string Symbol
        {
            get => _symbol;
            set
            {
                _symbol = value;
                OnPropertyChanged();
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void Update(double newPrice, double change, double changePercent, string newSymbol)
        {
            PriceChange = newPrice - Price;
            Price = newPrice;
            Change = change;
            ChangePercent = changePercent;
            Symbol = newSymbol;
        }
    }

    // 交易记录
    public class TradeRecord : INotifyPropertyChanged
    {
        private double _price;
        private double _quantity;
        private DateTime _time;
        private bool _isBuyerMaker;

        public double Price
        {
            get => _price;
            set
            {
                _price = value;
                OnPropertyChanged();
            }
        }

        public double Quantity
        {
            get => _quantity;
            set
            {
                _quantity = value;
                OnPropertyChanged();
            }
        }

        public DateTime Time
        {
            get => _time;
            set
            {
                _time = value;
                OnPropertyChanged();
            }
        }

        public bool IsBuyerMaker
        {
            get => _isBuyerMaker;
            set
            {
                _isBuyerMaker = value;
                OnPropertyChanged();
            }
        }

        public double Amount => Price * Quantity;

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }



    // 性能统计数据模型
    public class PerformanceStats : INotifyPropertyChanged
    {
        private double _latency;
        private int _updatesPerSecond;
        private double _cpuUsage;
        private double _memoryUsage;

        public double Latency
        {
            get => _latency;
            set
            {
                _latency = value;
                OnPropertyChanged();
            }
        }

        public int UpdatesPerSecond
        {
            get => _updatesPerSecond;
            set
            {
                _updatesPerSecond = value;
                OnPropertyChanged();
            }
        }

        public double CpuUsage
        {
            get => _cpuUsage;
            set
            {
                _cpuUsage = value;
                OnPropertyChanged();
            }
        }

        public double MemoryUsage
        {
            get => _memoryUsage;
            set
            {
                _memoryUsage = value;
                OnPropertyChanged();
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
