using System;
using System.Collections.Generic;
using System.Net.WebSockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Net.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using BinanceOrderBookWPF.Models;

namespace BinanceOrderBookWPF.Services
{
    public class BinanceWebSocketService : IDisposable
    {
        private readonly ILogger<BinanceWebSocketService> _logger;
        private readonly HttpClient _httpClient;
        private ClientWebSocket? _webSocket;
        private CancellationTokenSource? _cancellationTokenSource;
        private bool _disposed = false;
        private string? _currentSymbol;
        private Timer? _reconnectTimer;
        private Timer? _heartbeatTimer;
        private DateTime _lastMessageReceived = DateTime.UtcNow;
        private readonly object _connectionLock = new object();
        private bool _isConnecting = false;
        private readonly StringBuilder _messageBuffer = new();

        public event Action<OrderBookSnapshot>? OrderBookSnapshotReceived;
        public event Action<OrderBookUpdate>? OrderBookUpdateReceived;
        public event Action<AggTradeUpdate>? AggTradeReceived;
        public event Action<MiniTickerUpdate>? MiniTickerReceived;
        public event Action<string>? ErrorOccurred;
        public event Action<bool>? ConnectionStatusChanged;

        public BinanceWebSocketService(ILogger<BinanceWebSocketService> logger)
        {
            _logger = logger;
            _httpClient = new HttpClient();

            // 初始化心跳检测定时器
            _heartbeatTimer = new Timer(CheckHeartbeat, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
        }

        public bool IsConnected => _webSocket?.State == WebSocketState.Open;

        public async Task<OrderBookSnapshot?> GetOrderBookSnapshotAsync(string symbol)
        {
            try
            {
                var url = $"https://fapi.binance.com/fapi/v1/depth?symbol={symbol.ToUpper()}&limit=1000";
                var response = await _httpClient.GetStringAsync(url);
                var snapshot = JsonConvert.DeserializeObject<OrderBookSnapshot>(response);
                _logger.LogInformation($"获取订单薄快照成功，symbol: {symbol}, lastUpdateId: {snapshot?.LastUpdateId}");
                return snapshot;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取订单薄快照失败，symbol: {symbol}");
                ErrorOccurred?.Invoke($"获取订单薄快照失败: {ex.Message}");
                return null;
            }
        }

        public async Task<MiniTickerUpdate?> Get24HrTickerAsync(string symbol)
        {
            try
            {
                var url = $"https://fapi.binance.com/fapi/v1/24hrTicker?symbol={symbol.ToUpper()}";
                var response = await _httpClient.GetStringAsync(url);
                var ticker = JsonConvert.DeserializeObject<MiniTickerUpdate>(response);
                _logger.LogInformation($"获取24小时价格统计成功，symbol: {symbol}");
                return ticker;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取24小时价格统计失败，symbol: {symbol}");
                ErrorOccurred?.Invoke($"获取24小时价格统计失败: {ex.Message}");
                return null;
            }
        }

        public async Task ConnectAsync(string symbol)
        {
            lock (_connectionLock)
            {
                if (_isConnecting)
                {
                    _logger.LogInformation("连接正在进行中，跳过重复连接请求");
                    return;
                }
                _isConnecting = true;
            }

            try
            {
                _currentSymbol = symbol;
                await DisconnectAsync();

                _cancellationTokenSource = new CancellationTokenSource();
                _webSocket = new ClientWebSocket();

                var url = $"wss://fstream.binance.com/stream?streams={symbol.ToLower()}@depth/{symbol.ToLower()}@aggTrade/{symbol.ToLower()}@miniTicker";

                _logger.LogInformation($"连接WebSocket: {url}");
                await _webSocket.ConnectAsync(new Uri(url), _cancellationTokenSource.Token);

                _logger.LogInformation("WebSocket连接成功");
                _lastMessageReceived = DateTime.UtcNow;
                ConnectionStatusChanged?.Invoke(true);

                // 获取初始订单薄快照
                var snapshot = await GetOrderBookSnapshotAsync(symbol);
                if (snapshot != null)
                {
                    OrderBookSnapshotReceived?.Invoke(snapshot);
                }

                // 开始接收消息
                _ = Task.Run(async () => await ReceiveMessagesAsync(_cancellationTokenSource.Token));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "WebSocket连接失败");
                ErrorOccurred?.Invoke($"WebSocket连接失败: {ex.Message}");
                ConnectionStatusChanged?.Invoke(false);

                // 启动重连定时器
                StartReconnectTimer();
            }
            finally
            {
                lock (_connectionLock)
                {
                    _isConnecting = false;
                }
            }
        }

        public async Task DisconnectAsync()
        {
            try
            {
                // 停止重连定时器
                _reconnectTimer?.Dispose();
                _reconnectTimer = null;

                _cancellationTokenSource?.Cancel();

                if (_webSocket?.State == WebSocketState.Open)
                {
                    await _webSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Disconnecting", CancellationToken.None);
                }

                _webSocket?.Dispose();
                _webSocket = null;
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;

                _logger.LogInformation("WebSocket连接已断开");
                ConnectionStatusChanged?.Invoke(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "断开WebSocket连接时发生错误");
            }
        }

        private async Task ReceiveMessagesAsync(CancellationToken cancellationToken)
        {
            var buffer = new byte[1024 * 16]; // 增大缓冲区

            try
            {
                while (_webSocket?.State == WebSocketState.Open && !cancellationToken.IsCancellationRequested)
                {
                    var result = await _webSocket.ReceiveAsync(new ArraySegment<byte>(buffer), cancellationToken);

                    if (result.MessageType == WebSocketMessageType.Text)
                    {
                        var messagePart = Encoding.UTF8.GetString(buffer, 0, result.Count);
                        _lastMessageReceived = DateTime.UtcNow;

                        // 处理分片消息
                        _messageBuffer.Append(messagePart);

                        // 如果消息完整（以换行符结束或者是完整的JSON）
                        if (result.EndOfMessage)
                        {
                            var completeMessage = _messageBuffer.ToString();
                            _messageBuffer.Clear();
                            ProcessMessageAsync(completeMessage);
                        }
                    }
                    else if (result.MessageType == WebSocketMessageType.Close)
                    {
                        _logger.LogInformation("WebSocket连接被服务器关闭");
                        ConnectionStatusChanged?.Invoke(false);
                        StartReconnectTimer();
                        break;
                    }
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("WebSocket接收消息被取消");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "接收WebSocket消息时发生错误");
                ErrorOccurred?.Invoke($"接收消息错误: {ex.Message}");
                ConnectionStatusChanged?.Invoke(false);
                StartReconnectTimer();
            }
        }

        private void ProcessMessageAsync(string message)
        {
            try
            {
                // 验证消息是否为有效的JSON
                if (string.IsNullOrWhiteSpace(message) || !message.Trim().StartsWith("{") || !message.Trim().EndsWith("}"))
                {
                    _logger.LogWarning("收到无效的JSON消息: {Message}", message.Length > 100 ? message.Substring(0, 100) + "..." : message);
                    return;
                }

                var wsMessage = JsonConvert.DeserializeObject<WebSocketMessage>(message);
                if (wsMessage?.Data == null) return;

                var dataJson = wsMessage.Data.ToString();
                if (string.IsNullOrEmpty(dataJson)) return;

                var stream = wsMessage.Stream;
                _logger.LogDebug($"处理WebSocket消息: stream={stream}");

                if (stream.EndsWith("@depth"))
                {
                    var update = JsonConvert.DeserializeObject<OrderBookUpdate>(dataJson);
                    if (update != null)
                    {
                        OrderBookUpdateReceived?.Invoke(update);
                    }
                }
                else if (stream.EndsWith("@aggTrade"))
                {
                    var trade = JsonConvert.DeserializeObject<AggTradeUpdate>(dataJson);
                    if (trade != null)
                    {
                        _logger.LogInformation($"收到交易数据: 价格={trade.Price}, 数量={trade.Quantity}, 买方={!trade.IsBuyerMaker}");
                        AggTradeReceived?.Invoke(trade);
                    }
                }
                else if (stream.EndsWith("@miniTicker"))
                {
                    var ticker = JsonConvert.DeserializeObject<MiniTickerUpdate>(dataJson);
                    if (ticker != null)
                    {
                        MiniTickerReceived?.Invoke(ticker);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"处理WebSocket消息时发生错误: {message}");
            }
        }

        private void StartReconnectTimer()
        {
            if (_disposed || string.IsNullOrEmpty(_currentSymbol)) return;

            _reconnectTimer?.Dispose();
            _reconnectTimer = new Timer(async _ =>
            {
                if (!string.IsNullOrEmpty(_currentSymbol))
                {
                    _logger.LogInformation("尝试重新连接WebSocket...");
                    await ConnectAsync(_currentSymbol);
                }
            }, null, TimeSpan.FromSeconds(5), Timeout.InfiniteTimeSpan);
        }

        private void CheckHeartbeat(object? state)
        {
            if (_disposed || _webSocket?.State != WebSocketState.Open) return;

            var timeSinceLastMessage = DateTime.UtcNow - _lastMessageReceived;
            if (timeSinceLastMessage > TimeSpan.FromMinutes(2))
            {
                _logger.LogWarning($"心跳检测失败，上次收到消息时间: {timeSinceLastMessage.TotalSeconds:F0}秒前");
                ConnectionStatusChanged?.Invoke(false);
                StartReconnectTimer();
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _disposed = true;
                _heartbeatTimer?.Dispose();
                _reconnectTimer?.Dispose();
                DisconnectAsync().Wait();
                _httpClient?.Dispose();
            }
        }
    }
}
