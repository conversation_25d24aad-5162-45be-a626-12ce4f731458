using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using Microsoft.Extensions.Logging;
using BinanceOrderBookWPF.Models;

namespace BinanceOrderBookWPF.Services
{
    public class OrderBookService : INotifyPropertyChanged
    {
        private readonly ILogger<OrderBookService> _logger;
        private readonly SortedDictionary<long, double> _bids = new();
        private readonly SortedDictionary<long, double> _asks = new();
        private readonly object _lockObject = new();
        private long _lastUpdateId = 0;
        private DateTime _lastSnapshotTime = DateTime.MinValue;
        private int _updatesSinceSnapshot = 0;
        private const int MAX_UPDATES_BEFORE_RESYNC = 1000; // 1000次更新后重新同步
        private const int RESYNC_INTERVAL_MINUTES = 5; // 5分钟重新同步一次

        private int _depth = 10;
        private string _currentSymbol = "BTCUSDT";

        public ObservableCollection<OrderBookEntry> Bids { get; } = new();
        public ObservableCollection<OrderBookEntry> Asks { get; } = new();
        public ObservableCollection<TradeRecord> RecentTrades { get; } = new();
        public LastTrade LastTrade { get; } = new();

        private double _highestBid;
        private double _lowestAsk;

        public double HighestBid
        {
            get => _highestBid;
            private set
            {
                _highestBid = value;
                OnPropertyChanged();
            }
        }

        public double LowestAsk
        {
            get => _lowestAsk;
            private set
            {
                _lowestAsk = value;
                OnPropertyChanged();
            }
        }

        public double Spread => LowestAsk - HighestBid;

        public int Depth
        {
            get => _depth;
            set
            {
                if (_depth != value)
                {
                    _depth = value;
                    OnPropertyChanged();
                    UpdateDisplayedOrders();
                }
            }
        }

        public string CurrentSymbol
        {
            get => _currentSymbol;
            set
            {
                if (_currentSymbol != value)
                {
                    _currentSymbol = value;
                    OnPropertyChanged();
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        public event Action<string>? ResyncRequested;

        public OrderBookService(ILogger<OrderBookService> logger)
        {
            _logger = logger;
        }

        public void UpdateOrderBookSnapshot(OrderBookSnapshot snapshot)
        {
            lock (_lockObject)
            {
                _bids.Clear();
                _asks.Clear();

                _logger.LogInformation($"更新订单薄快照，买单数量: {snapshot.Bids.Count}, 卖单数量: {snapshot.Asks.Count}");

                foreach (var bid in snapshot.Bids)
                {
                    if (bid.Count >= 2 && 
                        double.TryParse(bid[0], out var price) && 
                        double.TryParse(bid[1], out var quantity))
                    {
                        var priceKey = (long)(price * 1000000);
                        _bids[priceKey] = quantity;
                    }
                }

                foreach (var ask in snapshot.Asks)
                {
                    if (ask.Count >= 2 && 
                        double.TryParse(ask[0], out var price) && 
                        double.TryParse(ask[1], out var quantity))
                    {
                        var priceKey = (long)(price * 1000000);
                        _asks[priceKey] = quantity;
                    }
                }

                _lastUpdateId = snapshot.LastUpdateId;
                _lastSnapshotTime = DateTime.UtcNow;
                _updatesSinceSnapshot = 0;
                UpdateDisplayedOrders();

                // 调试信息：显示价差
                if (_bids.Any() && _asks.Any())
                {
                    var highestBid = _bids.Keys.Max() / 1000000.0;
                    var lowestAsk = _asks.Keys.Min() / 1000000.0;
                    _logger.LogInformation($"价差检查 - 最高买价: {highestBid:F1}, 最低卖价: {lowestAsk:F1}, 价差: {lowestAsk - highestBid:F1}");
                }
            }
        }

        public void ApplyOrderBookUpdate(OrderBookUpdate update)
        {
            lock (_lockObject)
            {
                // 改进的更新ID检查逻辑
                var expectedNextId = _lastUpdateId + 1;
                var isValidUpdate = false;

                // 检查更新是否有效
                if (_lastUpdateId == 0) // 初始状态，接受任何更新
                {
                    isValidUpdate = true;
                }
                else if (update.FirstUpdateId <= expectedNextId && update.FinalUpdateId >= expectedNextId)
                {
                    // 正常的连续更新
                    isValidUpdate = true;
                }
                else if (update.FirstUpdateId > expectedNextId)
                {
                    // 跳跃更新，但仍然可以应用（减少警告频率）
                    var gap = update.FirstUpdateId - expectedNextId;
                    if (gap < 1000) // 只有当跳跃不太大时才警告
                    {
                        _logger.LogDebug($"订单薄更新ID跳跃，跳过 {gap} 个更新，从 {expectedNextId} 跳到 {update.FirstUpdateId}");
                    }
                    isValidUpdate = true;
                }
                else
                {
                    // 过时的更新，忽略
                    _logger.LogDebug($"忽略过时的订单薄更新: {update.FirstUpdateId}-{update.FinalUpdateId}, 当前ID: {_lastUpdateId}");
                    return;
                }

                if (isValidUpdate)
                {
                    var bidUpdates = 0;
                    var askUpdates = 0;

                    // 应用买单更新
                    foreach (var bid in update.Bids)
                    {
                        if (bid.Count >= 2 &&
                            double.TryParse(bid[0], out var price) &&
                            double.TryParse(bid[1], out var quantity))
                        {
                            var priceKey = (long)(price * 1000000);
                            if (quantity == 0)
                            {
                                _bids.Remove(priceKey);
                            }
                            else
                            {
                                _bids[priceKey] = quantity;
                            }
                            bidUpdates++;
                        }
                    }

                    // 应用卖单更新
                    foreach (var ask in update.Asks)
                    {
                        if (ask.Count >= 2 &&
                            double.TryParse(ask[0], out var price) &&
                            double.TryParse(ask[1], out var quantity))
                        {
                            var priceKey = (long)(price * 1000000);
                            if (quantity == 0)
                            {
                                _asks.Remove(priceKey);
                            }
                            else
                            {
                                _asks[priceKey] = quantity;
                            }
                            askUpdates++;
                        }
                    }

                    _lastUpdateId = update.FinalUpdateId;
                    _updatesSinceSnapshot++;
                    UpdateDisplayedOrders();

                    // 只在有实际更新时记录调试信息
                    if (bidUpdates > 0 || askUpdates > 0)
                    {
                        _logger.LogTrace($"订单薄更新: 买单{bidUpdates}条, 卖单{askUpdates}条, UpdateId: {update.FinalUpdateId}");
                    }

                    // 检查是否需要重新同步
                    CheckForResyncNeed();
                }
            }
        }

        public void AddTrade(AggTradeUpdate trade)
        {
            _logger.LogInformation($"OrderBookService.AddTrade: 收到交易数据 价格={trade.Price}, 数量={trade.Quantity}");

            if (double.TryParse(trade.Price, out var price) &&
                double.TryParse(trade.Quantity, out var quantity))
            {
                var tradeRecord = new TradeRecord
                {
                    Price = price,
                    Quantity = quantity,
                    Time = DateTimeOffset.FromUnixTimeMilliseconds(trade.TradeTime).DateTime,
                    IsBuyerMaker = trade.IsBuyerMaker
                };

                _logger.LogInformation($"OrderBookService.AddTrade: 创建交易记录 价格={price:F2}, 数量={quantity:F4}, 买方={!trade.IsBuyerMaker}");

                // 在UI线程上更新
                App.Current.Dispatcher.Invoke(() =>
                {
                    RecentTrades.Insert(0, tradeRecord);
                    _logger.LogInformation($"OrderBookService.AddTrade: 添加到RecentTrades，当前总数={RecentTrades.Count}");

                    // 保持最近100条交易记录
                    while (RecentTrades.Count > 100)
                    {
                        RecentTrades.RemoveAt(RecentTrades.Count - 1);
                    }
                });
            }
            else
            {
                _logger.LogWarning($"OrderBookService.AddTrade: 无法解析价格或数量 价格={trade.Price}, 数量={trade.Quantity}");
            }
        }

        public void UpdateLastTrade(MiniTickerUpdate ticker)
        {
            if (double.TryParse(ticker.ClosePrice, out var closePrice) &&
                double.TryParse(ticker.OpenPrice, out var openPrice))
            {
                var change = closePrice - openPrice;
                var changePercent = openPrice != 0 ? (change / openPrice) * 100 : 0;

                App.Current.Dispatcher.Invoke(() =>
                {
                    LastTrade.Update(closePrice, change, changePercent, ticker.Symbol);
                });
            }
        }

        private void UpdateDisplayedOrders()
        {
            App.Current.Dispatcher.Invoke(() =>
            {
                // 更新买单显示（按价格降序）
                Bids.Clear();
                var topBids = _bids.OrderByDescending(x => x.Key).Take(_depth).ToList();
                double totalBidAmount = 0;
                double maxBidQuantity = topBids.Any() ? topBids.Max(x => x.Value) : 1;

                foreach (var bid in topBids)
                {
                    var price = bid.Key / 1000000.0;
                    var quantity = bid.Value;
                    var amount = price * quantity;
                    totalBidAmount += amount;

                    Bids.Add(new OrderBookEntry
                    {
                        Price = price,
                        Quantity = quantity,
                        Total = totalBidAmount,
                        RelativeDepth = quantity / maxBidQuantity,
                        IsBid = true
                    });
                }

                // 更新卖单显示（按价格降序，最高价在上方，最低价在下方与买方衔接）
                Asks.Clear();
                var topAsks = _asks.OrderBy(x => x.Key).Take(_depth).OrderByDescending(x => x.Key).ToList();
                double totalAskAmount = 0;
                double maxAskQuantity = topAsks.Any() ? topAsks.Max(x => x.Value) : 1;

                foreach (var ask in topAsks)
                {
                    var price = ask.Key / 1000000.0;
                    var quantity = ask.Value;
                    var amount = price * quantity;
                    totalAskAmount += amount;

                    Asks.Add(new OrderBookEntry
                    {
                        Price = price,
                        Quantity = quantity,
                        Total = totalAskAmount,
                        RelativeDepth = quantity / maxAskQuantity,
                        IsBid = false
                    });
                }

                // 更新价差信息
                if (topBids.Any() && topAsks.Any())
                {
                    HighestBid = topBids.First().Key / 1000000.0;
                    LowestAsk = topAsks.First().Key / 1000000.0;
                }
            });
        }

        private void CheckForResyncNeed()
        {
            var timeSinceLastSnapshot = DateTime.UtcNow - _lastSnapshotTime;

            // 如果更新次数过多或时间间隔过长，请求重新同步
            if (_updatesSinceSnapshot >= MAX_UPDATES_BEFORE_RESYNC ||
                timeSinceLastSnapshot.TotalMinutes >= RESYNC_INTERVAL_MINUTES)
            {
                _logger.LogInformation($"请求重新同步订单薄 - 更新次数: {_updatesSinceSnapshot}, 时间间隔: {timeSinceLastSnapshot.TotalMinutes:F1}分钟");
                ResyncRequested?.Invoke(_currentSymbol);
            }
        }

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
