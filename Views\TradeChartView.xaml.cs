using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Threading;
using BinanceOrderBookWPF.Models;
using BinanceOrderBookWPF.Services;
using ScottPlot;
using ScottPlot.WPF;

namespace BinanceOrderBookWPF.Views
{
    public partial class TradeChartView : UserControl
    {
        private OrderBookService? _orderBookService;
        private readonly List<TradePoint> _tradePoints = new();

        // 图表参数
        private DateTime _startTime = DateTime.UtcNow;
        private TimeSpan _timeWindow = TimeSpan.FromMinutes(30); // 可变时间窗口，默认30分钟
        private readonly TimeSpan _maxTimeWindow = TimeSpan.FromHours(4); // 最大4小时
        private readonly TimeSpan _minTimeWindow = TimeSpan.FromMinutes(1); // 最小1分钟

        // 防抖机制
        private readonly DispatcherTimer _redrawTimer;
        private bool _needsRedraw = false;

        // ScottPlot 散点图
        private ScottPlot.Plottables.Scatter? _buyScatter;
        private ScottPlot.Plottables.Scatter? _sellScatter;

        // 用户缩放状态跟踪
        private bool _userHasZoomed = false;
        private double _lastAutoXMin, _lastAutoXMax, _lastAutoYMin, _lastAutoYMax;
        private double _userXMin, _userXMax, _userYMin, _userYMax;
        private bool _isSettingAutoRange = false; // 标志：正在设置自动范围

        // 颜色定义
        private readonly ScottPlot.Color _buyColor = ScottPlot.Color.FromHex("#0ECB81");
        private readonly ScottPlot.Color _sellColor = ScottPlot.Color.FromHex("#F84960");
        private readonly ScottPlot.Color _backgroundColor = ScottPlot.Color.FromHex("#1E1E1E");
        private readonly ScottPlot.Color _gridColor = ScottPlot.Color.FromHex("#2B3139");
        private readonly ScottPlot.Color _textColor = ScottPlot.Color.FromHex("#FFFFFF");

        public TradeChartView()
        {
            System.Diagnostics.Debug.WriteLine("TradeChartView: 构造函数开始");
            InitializeComponent();
            Loaded += TradeChartView_Loaded;
            DataContextChanged += TradeChartView_DataContextChanged;

            // 初始化防抖定时器
            _redrawTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(100) // 100ms防抖延迟，提供实时显示效果
            };
            _redrawTimer.Tick += RedrawTimer_Tick;

            System.Diagnostics.Debug.WriteLine("TradeChartView: 构造函数完成");
        }

        private void TradeChartView_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine($"TradeChartView_DataContextChanged: 旧值={e.OldValue?.GetType()?.Name}, 新值={e.NewValue?.GetType()?.Name}");

            if (e.NewValue is OrderBookService orderBookService)
            {
                System.Diagnostics.Debug.WriteLine($"TradeChartView_DataContextChanged: 获得OrderBookService，交易数量={orderBookService.RecentTrades?.Count ?? 0}");

                // 如果已经加载完成，立即处理
                if (IsLoaded)
                {
                    System.Diagnostics.Debug.WriteLine("TradeChartView_DataContextChanged: 控件已加载，立即处理DataContext");
                    HandleDataContextChange(orderBookService);
                }
            }
        }

        private void HandleDataContextChange(OrderBookService orderBookService)
        {
            System.Diagnostics.Debug.WriteLine("TradeChartView.HandleDataContextChange: 开始处理");
            Console.WriteLine("TradeChartView.HandleDataContextChange: 开始处理");

            // 取消之前的订阅
            if (_orderBookService != null)
            {
                _orderBookService.RecentTrades.CollectionChanged -= RecentTrades_CollectionChanged;
                System.Diagnostics.Debug.WriteLine("TradeChartView.HandleDataContextChange: 取消之前的订阅");
                Console.WriteLine("TradeChartView.HandleDataContextChange: 取消之前的订阅");
            }

            _orderBookService = orderBookService;
            _orderBookService.RecentTrades.CollectionChanged += RecentTrades_CollectionChanged;

            System.Diagnostics.Debug.WriteLine($"TradeChartView.HandleDataContextChange: 找到OrderBookService，现有交易数量: {_orderBookService.RecentTrades?.Count ?? 0}");
            Console.WriteLine($"TradeChartView.HandleDataContextChange: 找到OrderBookService，现有交易数量: {_orderBookService.RecentTrades?.Count ?? 0}");

            // 加载现有的交易数据
            LoadExistingTrades();

            // 更新图表
            UpdateChart();

            System.Diagnostics.Debug.WriteLine("TradeChartView.HandleDataContextChange: 处理完成");
            Console.WriteLine("TradeChartView.HandleDataContextChange: 处理完成");
        }

        private void TradeChartView_Loaded(object sender, RoutedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("TradeChartView_Loaded: 开始加载");

            // 初始化 ScottPlot
            InitializeChart();

            // 初始化时间起点
            _startTime = DateTime.UtcNow;

            // 初始化时间窗口显示
            UpdateTimeWindowDisplay();

            if (DataContext is OrderBookService orderBookService)
            {
                System.Diagnostics.Debug.WriteLine("TradeChartView_Loaded: DataContext是OrderBookService，处理数据");
                HandleDataContextChange(orderBookService);
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"TradeChartView_Loaded: DataContext不是OrderBookService，类型={DataContext?.GetType()?.Name ?? "null"}，添加测试数据");
                // 添加一些测试数据
                AddTestData();
                // 更新图表
                UpdateChart();
            }
        }



        private void LoadExistingTrades()
        {
            if (_orderBookService?.RecentTrades != null)
            {
                System.Diagnostics.Debug.WriteLine($"LoadExistingTrades: 开始加载 {_orderBookService.RecentTrades.Count} 个现有交易");
                foreach (var trade in _orderBookService.RecentTrades)
                {
                    AddTradePoint(trade);
                }
                System.Diagnostics.Debug.WriteLine($"LoadExistingTrades: 完成，总交易点数: {_tradePoints.Count}");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("LoadExistingTrades: 没有找到交易数据");
            }
        }

        private void AddTestData()
        {
            System.Diagnostics.Debug.WriteLine("AddTestData: 添加测试数据");
            var basePrice = 117600.0;
            var now = DateTime.UtcNow;

            // 添加一些测试交易点，使用更大的数量差异来展示气泡大小变化
            for (int i = 0; i < 20; i++)
            {
                // 创建更明显的数量差异：小交易(0.01-0.1)、中等交易(0.5-1.0)、大交易(2.0-5.0)
                double quantity;
                if (i < 7)
                {
                    quantity = 0.01 + (i * 0.015); // 小交易：0.01 到 0.1
                }
                else if (i < 14)
                {
                    quantity = 0.5 + ((i - 7) * 0.07); // 中等交易：0.5 到 1.0
                }
                else
                {
                    quantity = 2.0 + ((i - 14) * 0.5); // 大交易：2.0 到 5.0
                }

                var tradePoint = new TradePoint
                {
                    Price = basePrice + (i % 2 == 0 ? 1 : -1) * (i * 0.5),
                    Quantity = quantity,
                    Time = now.AddSeconds(-60 + i * 3),
                    IsBuyerMaker = i % 2 == 0,
                    Amount = (basePrice + (i % 2 == 0 ? 1 : -1) * (i * 0.5)) * quantity
                };

                _tradePoints.Add(tradePoint);
            }

            System.Diagnostics.Debug.WriteLine($"AddTestData: 完成，添加了 {_tradePoints.Count} 个测试交易点，数量范围: {_tradePoints.Min(tp => tp.Quantity):F3} - {_tradePoints.Max(tp => tp.Quantity):F3}");
        }

        private void TradeChartView_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            RequestRedraw();
        }

        private void InitializeChart()
        {
            System.Diagnostics.Debug.WriteLine("InitializeChart: 初始化 ScottPlot 图表");

            // 设置图表背景颜色
            TradeChart.Plot.FigureBackground.Color = _backgroundColor;
            TradeChart.Plot.DataBackground.Color = _backgroundColor;

            // 设置网格样式
            TradeChart.Plot.Grid.MajorLineColor = _gridColor;

            // 设置坐标轴样式
            TradeChart.Plot.Axes.Bottom.Label.Text = "时间";
            TradeChart.Plot.Axes.Left.Label.Text = "价格";

            // 设置坐标轴颜色为白色（适配暗黑主题）
            TradeChart.Plot.Axes.Bottom.Label.ForeColor = _textColor;
            TradeChart.Plot.Axes.Left.Label.ForeColor = _textColor;
            TradeChart.Plot.Axes.Bottom.TickLabelStyle.ForeColor = _textColor;
            TradeChart.Plot.Axes.Left.TickLabelStyle.ForeColor = _textColor;
            TradeChart.Plot.Axes.Bottom.MajorTickStyle.Color = _textColor;
            TradeChart.Plot.Axes.Left.MajorTickStyle.Color = _textColor;
            TradeChart.Plot.Axes.Bottom.MinorTickStyle.Color = _textColor;
            TradeChart.Plot.Axes.Left.MinorTickStyle.Color = _textColor;
            TradeChart.Plot.Axes.Bottom.FrameLineStyle.Color = _textColor;
            TradeChart.Plot.Axes.Left.FrameLineStyle.Color = _textColor;

            // 初始化散点图
            _buyScatter = TradeChart.Plot.Add.Scatter(new double[0], new double[0]);
            _buyScatter.Color = _buyColor;
            _buyScatter.MarkerSize = 8;
            _buyScatter.MarkerShape = ScottPlot.MarkerShape.FilledCircle;
            _buyScatter.LineWidth = 0;
            _buyScatter.LegendText = "买入";

            _sellScatter = TradeChart.Plot.Add.Scatter(new double[0], new double[0]);
            _sellScatter.Color = _sellColor;
            _sellScatter.MarkerSize = 8;
            _sellScatter.MarkerShape = ScottPlot.MarkerShape.FilledCircle;
            _sellScatter.LineWidth = 0;
            _sellScatter.LegendText = "卖出";

            // 显示图例
            TradeChart.Plot.ShowLegend();

            // 添加鼠标事件监听器来检测用户手动缩放
            TradeChart.MouseUp += TradeChart_MouseUp;
            TradeChart.MouseWheel += TradeChart_MouseWheel;
            TradeChart.MouseMove += TradeChart_MouseMove;

            System.Diagnostics.Debug.WriteLine("InitializeChart: 完成");
        }

        private void TradeChart_MouseUp(object? sender, MouseButtonEventArgs e)
        {
            // 在鼠标释放后检测是否发生了缩放（拖拽缩放）
            CheckForUserZoom("MouseUp");
        }

        private void TradeChart_MouseWheel(object? sender, MouseWheelEventArgs e)
        {
            // 鼠标滚轮缩放后立即检测
            System.Diagnostics.Debug.WriteLine($"检测到鼠标滚轮事件: Delta={e.Delta}");

            // 延迟一点检测，让ScottPlot完成缩放操作
            Dispatcher.BeginInvoke(new Action(() => {
                CheckForUserZoom("MouseWheel");
            }), DispatcherPriority.Background);
        }

        private void TradeChart_MouseMove(object? sender, MouseEventArgs e)
        {
            // 如果是拖拽状态（按住鼠标移动），检测缩放
            if (e.LeftButton == MouseButtonState.Pressed || e.RightButton == MouseButtonState.Pressed)
            {
                // 延迟检测，避免过于频繁
                Dispatcher.BeginInvoke(new Action(() => {
                    CheckForUserZoom("MouseMove");
                }), DispatcherPriority.Background);
            }
        }

        private void CheckForUserZoom(string source)
        {
            // 如果正在设置自动范围，跳过检测
            if (_isSettingAutoRange)
            {
                System.Diagnostics.Debug.WriteLine($"[{source}] 跳过缩放检测：正在设置自动范围");
                return;
            }

            // 检测用户是否手动缩放了图表
            var currentLimits = TradeChart.Plot.Axes.GetLimits();

            // 如果当前显示的范围与最后一次自动设置的范围不同，说明用户进行了手动缩放
            if (_lastAutoXMin != 0 || _lastAutoXMax != 0 || _lastAutoYMin != 0 || _lastAutoYMax != 0)
            {
                const double tolerance = 1e-6; // 浮点数比较容差
                bool xChanged = Math.Abs(currentLimits.Left - _lastAutoXMin) > tolerance ||
                               Math.Abs(currentLimits.Right - _lastAutoXMax) > tolerance;
                bool yChanged = Math.Abs(currentLimits.Bottom - _lastAutoYMin) > tolerance ||
                               Math.Abs(currentLimits.Top - _lastAutoYMax) > tolerance;

                if (xChanged || yChanged)
                {
                    _userHasZoomed = true;
                    _userXMin = currentLimits.Left;
                    _userXMax = currentLimits.Right;
                    _userYMin = currentLimits.Bottom;
                    _userYMax = currentLimits.Top;

                    System.Diagnostics.Debug.WriteLine($"[{source}] 检测到用户缩放: X[{_userXMin:F2}, {_userXMax:F2}], Y[{_userYMin:F2}, {_userYMax:F2}]");
                }
            }
        }

        private void RedrawTimer_Tick(object? sender, EventArgs e)
        {
            _redrawTimer.Stop();
            if (_needsRedraw)
            {
                _needsRedraw = false;
                UpdateChart();
            }
        }

        private void RequestRedraw()
        {
            _needsRedraw = true;
            _redrawTimer.Stop();
            _redrawTimer.Start();
        }

        private void RecentTrades_CollectionChanged(object? sender, NotifyCollectionChangedEventArgs e)
        {
            // 减少调试输出的频率，避免控制台输出过多
            if (e.Action == NotifyCollectionChangedAction.Add && e.NewItems != null)
            {
                foreach (TradeRecord trade in e.NewItems)
                {
                    AddTradePoint(trade);
                }
            }

            // 清理过期的交易点
            CleanupOldTrades();

            // 使用防抖机制请求重绘，避免频繁更新UI
            RequestRedraw();
        }

        private void AddTradePoint(TradeRecord trade)
        {
            var tradePoint = new TradePoint
            {
                Price = trade.Price,
                Quantity = trade.Quantity,
                Time = trade.Time,
                IsBuyerMaker = trade.IsBuyerMaker,
                Amount = trade.Price * trade.Quantity
            };

            _tradePoints.Add(tradePoint);
        }

        private void CleanupOldTrades()
        {
            // 使用UTC时间，因为交易记录的时间戳是UTC时间
            // 使用最大时间窗口来保留数据，这样缩放时不会丢失数据
            var cutoffTime = DateTime.UtcNow - _maxTimeWindow;
            var beforeCount = _tradePoints.Count;

            _tradePoints.RemoveAll(tp => tp.Time < cutoffTime);
            var afterCount = _tradePoints.Count;

            // 只在有实际清理时才输出调试信息
            if (beforeCount != afterCount)
            {
                System.Diagnostics.Debug.WriteLine($"TradeChartView.CleanupOldTrades: 清理完成，删除了{beforeCount - afterCount}个交易点，剩余{afterCount}个");
            }
        }

        private void UpdateChart()
        {
            System.Diagnostics.Debug.WriteLine($"UpdateChart: 开始更新图表，交易点数量: {_tradePoints.Count}");

            // 在重绘之前保存当前的缩放状态（如果用户已经缩放过）
            bool shouldRestoreUserZoom = _userHasZoomed;
            double savedUserXMin = _userXMin, savedUserXMax = _userXMax;
            double savedUserYMin = _userYMin, savedUserYMax = _userYMax;

            if (_tradePoints.Count == 0)
            {
                // 清空图表
                TradeChart.Plot.Clear();
                TradeChart.Refresh();
                return;
            }

            // 获取当前时间窗口内的可见交易点
            var now = DateTime.UtcNow;
            var visibleTrades = _tradePoints.Where(tp => (now - tp.Time) <= _timeWindow).ToList();

            if (!visibleTrades.Any())
            {
                // 清空图表
                TradeChart.Plot.Clear();
                TradeChart.Refresh();
                return;
            }

            // 分离买入和卖出交易
            var buyTrades = visibleTrades.Where(tp => !tp.IsBuyerMaker).ToList(); // IsBuyerMaker为false表示买入
            var sellTrades = visibleTrades.Where(tp => tp.IsBuyerMaker).ToList(); // IsBuyerMaker为true表示卖出

            // 清空现有的散点图
            TradeChart.Plot.Clear();

            // 计算数量范围用于气泡大小缩放
            var allQuantities = visibleTrades.Select(tp => tp.Quantity).ToList();
            var minQuantity = allQuantities.Any() ? allQuantities.Min() : 0;
            var maxQuantity = allQuantities.Any() ? allQuantities.Max() : 1;
            var quantityRange = maxQuantity - minQuantity;

            // 气泡大小参数
            const double minBubbleSize = 1.0;
            const double maxBubbleSize = 50.0;

            // 单独绘制每个买入交易点
            foreach (var trade in buyTrades)
            {
                var bubbleSize = CalculateBubbleSize(trade.Quantity, minQuantity, maxQuantity, quantityRange, minBubbleSize, maxBubbleSize);
                var scatter = TradeChart.Plot.Add.Scatter(new double[] { trade.Time.ToOADate() }, new double[] { trade.Price });
                scatter.Color = _buyColor;
                scatter.MarkerSize = (float)bubbleSize;
                scatter.MarkerShape = ScottPlot.MarkerShape.FilledCircle;
                scatter.LineWidth = 0;
            }

            // 单独绘制每个卖出交易点
            foreach (var trade in sellTrades)
            {
                var bubbleSize = CalculateBubbleSize(trade.Quantity, minQuantity, maxQuantity, quantityRange, minBubbleSize, maxBubbleSize);
                var scatter = TradeChart.Plot.Add.Scatter(new double[] { trade.Time.ToOADate() }, new double[] { trade.Price });
                scatter.Color = _sellColor;
                scatter.MarkerSize = (float)bubbleSize;
                scatter.MarkerShape = ScottPlot.MarkerShape.FilledCircle;
                scatter.LineWidth = 0;
            }

            // 添加图例（使用固定大小的散点图作为图例）
            if (buyTrades.Any())
            {
                _buyScatter = TradeChart.Plot.Add.Scatter(new double[0], new double[0]);
                _buyScatter.Color = _buyColor;
                _buyScatter.MarkerSize = 8;
                _buyScatter.MarkerShape = ScottPlot.MarkerShape.FilledCircle;
                _buyScatter.LineWidth = 0;
                _buyScatter.LegendText = "买入";
            }

            if (sellTrades.Any())
            {
                _sellScatter = TradeChart.Plot.Add.Scatter(new double[0], new double[0]);
                _sellScatter.Color = _sellColor;
                _sellScatter.MarkerSize = 8;
                _sellScatter.MarkerShape = ScottPlot.MarkerShape.FilledCircle;
                _sellScatter.LineWidth = 0;
                _sellScatter.LegendText = "卖出";
            }

            // 设置坐标轴范围
            if (visibleTrades.Any())
            {
                var minTime = visibleTrades.Min(tp => tp.Time.ToOADate());
                var maxTime = visibleTrades.Max(tp => tp.Time.ToOADate());
                var minPrice = visibleTrades.Min(tp => tp.Price);
                var maxPrice = visibleTrades.Max(tp => tp.Price);

                // 添加一些边距
                var timeRange = maxTime - minTime;
                var priceRange = maxPrice - minPrice;

                if (timeRange > 0 && priceRange > 0)
                {
                    // 计算自动范围
                    var autoXMin = minTime - timeRange * 0.05;
                    var autoXMax = maxTime + timeRange * 0.05;
                    var autoYMin = minPrice - priceRange * 0.1;
                    var autoYMax = maxPrice + priceRange * 0.1;

                    // 如果用户没有手动缩放，使用自动计算的范围
                    if (!shouldRestoreUserZoom)
                    {
                        _isSettingAutoRange = true; // 标记开始设置自动范围

                        TradeChart.Plot.Axes.SetLimits(autoXMin, autoXMax, autoYMin, autoYMax);

                        // 保存自动设置的范围，用于后续检测用户缩放
                        _lastAutoXMin = autoXMin;
                        _lastAutoXMax = autoXMax;
                        _lastAutoYMin = autoYMin;
                        _lastAutoYMax = autoYMax;

                        _isSettingAutoRange = false; // 标记结束设置自动范围

                        System.Diagnostics.Debug.WriteLine($"使用自动范围: X[{autoXMin:F2}, {autoXMax:F2}], Y[{autoYMin:F2}, {autoYMax:F2}]");
                    }
                    else
                    {
                        // 用户已手动缩放，恢复用户的缩放状态
                        _isSettingAutoRange = true; // 标记开始设置范围

                        TradeChart.Plot.Axes.SetLimits(savedUserXMin, savedUserXMax, savedUserYMin, savedUserYMax);

                        _isSettingAutoRange = false; // 标记结束设置范围

                        System.Diagnostics.Debug.WriteLine($"恢复用户缩放: X[{savedUserXMin:F2}, {savedUserXMax:F2}], Y[{savedUserYMin:F2}, {savedUserYMax:F2}]");
                    }
                }
            }

            // 显示图例
            TradeChart.Plot.ShowLegend();

            // 刷新图表
            TradeChart.Refresh();

            // 在图表刷新后，延迟检测一下缩放状态（以防有遗漏）
            Dispatcher.BeginInvoke(new Action(() => {
                if (!_isSettingAutoRange && !shouldRestoreUserZoom)
                {
                    // 只有在没有恢复用户缩放的情况下才进行检测
                    CheckForUserZoom("AfterRefresh");
                }
            }), DispatcherPriority.Background);

            System.Diagnostics.Debug.WriteLine($"UpdateChart: 完成，买入点: {buyTrades.Count}, 卖出点: {sellTrades.Count}");
        }

        /// <summary>
        /// 根据成交数量计算气泡大小
        /// </summary>
        /// <param name="quantity">当前交易数量</param>
        /// <param name="minQuantity">最小数量</param>
        /// <param name="maxQuantity">最大数量</param>
        /// <param name="quantityRange">数量范围</param>
        /// <param name="minBubbleSize">最小气泡大小</param>
        /// <param name="maxBubbleSize">最大气泡大小</param>
        /// <returns>计算出的气泡大小</returns>
        private double CalculateBubbleSize(double quantity, double minQuantity, double maxQuantity, double quantityRange, double minBubbleSize, double maxBubbleSize)
        {
            // 如果数量范围为0（所有交易数量相同），返回中等大小
            if (quantityRange <= 0)
            {
                return (minBubbleSize + maxBubbleSize) / 2;
            }

            // 线性缩放：将数量范围映射到气泡大小范围
            var normalizedQuantity = (quantity - minQuantity) / quantityRange;
            var bubbleSize = minBubbleSize + (normalizedQuantity * (maxBubbleSize - minBubbleSize));

            // 确保气泡大小在指定范围内
            return Math.Max(minBubbleSize, Math.Min(maxBubbleSize, bubbleSize));
        }

        private void UpdateTimeWindowDisplay()
        {
            if (TimeWindowDisplay != null)
            {
                var totalMinutes = _timeWindow.TotalMinutes;
                string displayText;

                if (totalMinutes < 60)
                {
                    displayText = $"{totalMinutes:F0}分钟";
                }
                else
                {
                    var hours = totalMinutes / 60;
                    displayText = $"{hours:F1}小时";
                }

                TimeWindowDisplay.Text = displayText;
            }
        }

        /// <summary>
        /// 重置用户缩放状态，回到自动缩放模式
        /// </summary>
        public void ResetUserZoom()
        {
            _userHasZoomed = false;
            _userXMin = _userXMax = _userYMin = _userYMax = 0;
            _lastAutoXMin = _lastAutoXMax = _lastAutoYMin = _lastAutoYMax = 0;
            System.Diagnostics.Debug.WriteLine("用户缩放状态已重置，回到自动缩放模式");

            // 立即更新图表以应用自动缩放
            RequestRedraw();
        }
    }

    // 交易点数据结构
    public class TradePoint
    {
        public double Price { get; set; }
        public double Quantity { get; set; }
        public DateTime Time { get; set; }
        public bool IsBuyerMaker { get; set; }
        public double Amount { get; set; }
    }
}
