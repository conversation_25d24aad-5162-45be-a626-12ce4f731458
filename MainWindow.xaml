<Window x:Class="BinanceOrderBookWPF.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:BinanceOrderBookWPF"
        xmlns:views="clr-namespace:BinanceOrderBookWPF.Views"
        xmlns:converters="clr-namespace:BinanceOrderBookWPF"
        mc:Ignorable="d"
        Title="Binance OrderBook" Height="800" Width="1000"
        Background="#FF121622"
        Loaded="Window_Loaded">

    <Window.Resources>
        <converters:BoolToColorConverter x:Key="BoolToColorConverter"/>
        <converters:PrecisionFormatConverter x:Key="PrecisionFormatConverter"/>
        <converters:PriceChangeToColorConverter x:Key="PriceChangeToColorConverter"/>
        <converters:DepthToWidthConverter x:Key="DepthToWidthConverter"/>
        <converters:LatencyToColorConverter x:Key="LatencyToColorConverter"/>
    </Window.Resources>
    
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 顶部控制栏 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
            <TextBox x:Name="SymbolTextBox" Text="{Binding CurrentSymbol}"
                     Width="140" Height="36" VerticalContentAlignment="Center"
                     Style="{StaticResource ModernTextBoxStyle}"/>
            <Button x:Name="QueryButton" Content="查询" Width="80" Height="36" Margin="12,0,0,0"
                    Style="{StaticResource ModernButtonStyle}"
                    Click="QueryButton_Click"/>
            <TextBlock Text="{Binding CurrentSymbol}"
                       Style="{StaticResource TitleTextStyle}"
                       Foreground="{StaticResource WarningColor}"
                       VerticalAlignment="Center" Margin="24,0,0,0"/>
        </StackPanel>
        
        <!-- 历史记录下拉框 -->
        <ComboBox x:Name="HistoryComboBox" Grid.Row="1" Width="220" Height="36"
                  HorizontalAlignment="Left" Margin="0,0,0,16"
                  Style="{StaticResource ModernComboBoxStyle}"
                  SelectionChanged="HistoryComboBox_SelectionChanged"/>
        
        <!-- 价格信息显示 - 使用新的专业价格视图 -->
        <views:PriceInfoView x:Name="PriceInfoView" Grid.Row="2" DataContext="{Binding}" Margin="0,0,0,20"/>

        <!-- 主要内容区域 -->
        <Grid Grid.Row="3">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1.5*"/>
                <ColumnDefinition Width="1.5*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 实时交易图表 - 新增的可视化面板 -->
            <views:TradeChartView Grid.Column="0" Margin="0,0,10,0" DataContext="{Binding}"/>

            <!-- 订单薄显示 - 使用新的专业深度视图 -->
            <views:OrderBookDepthView Grid.Column="1" Margin="0,0,10,0" DataContext="{Binding}"/>

            <!-- 交易记录显示 - 使用新的专业组件 -->
            <views:TradeTickerView Grid.Column="2" DataContext="{Binding}"/>
        </Grid>
        
        <!-- 高级控制面板 -->
        <views:AdvancedControlPanel x:Name="AdvancedControlPanel" Grid.Row="4" DataContext="{Binding}" Margin="0,16,0,0"/>
    </Grid>
</Window>
