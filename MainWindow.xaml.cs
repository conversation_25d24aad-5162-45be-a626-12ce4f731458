using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using BinanceOrderBookWPF.Services;
using BinanceOrderBookWPF.Models;
using BinanceOrderBookWPF.Views;

namespace BinanceOrderBookWPF
{
    /// <summary>
    /// MainWindow的代码后台逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly BinanceWebSocketService _webSocketService;
        private readonly OrderBookService _orderBookService;
        private readonly ILogger<MainWindow> _logger;
        private readonly List<string> _symbolHistory = new();

        public MainWindow()
        {
            InitializeComponent();
            
            // 配置依赖注入
            var services = new ServiceCollection();
            ConfigureServices(services);
            var serviceProvider = services.BuildServiceProvider();

            _webSocketService = serviceProvider.GetRequiredService<BinanceWebSocketService>();
            _orderBookService = serviceProvider.GetRequiredService<OrderBookService>();
            _logger = serviceProvider.GetRequiredService<ILogger<MainWindow>>();

            // 设置数据上下文
            DataContext = _orderBookService;

            // 初始化历史记录
            _symbolHistory.Add("BTCUSDT");
            UpdateHistoryComboBox();

            // 订阅WebSocket事件
            SubscribeToWebSocketEvents();

            // 启动连接
            _ = ConnectToSymbolAsync(_orderBookService.CurrentSymbol);
        }

        private void ConfigureServices(IServiceCollection services)
        {
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Information);
            });

            services.AddSingleton<BinanceWebSocketService>();
            services.AddSingleton<OrderBookService>();
        }

        private void SubscribeToWebSocketEvents()
        {
            _webSocketService.OrderBookSnapshotReceived += OnOrderBookSnapshotReceived;
            _webSocketService.OrderBookUpdateReceived += OnOrderBookUpdateReceived;
            _webSocketService.AggTradeReceived += OnAggTradeReceived;
            _webSocketService.MiniTickerReceived += OnMiniTickerReceived;
            _webSocketService.ErrorOccurred += OnErrorOccurred;
            _webSocketService.ConnectionStatusChanged += OnConnectionStatusChanged;

            // 订阅重新同步请求
            _orderBookService.ResyncRequested += OnResyncRequested;
        }

        private void OnOrderBookSnapshotReceived(OrderBookSnapshot snapshot)
        {
            _orderBookService.UpdateOrderBookSnapshot(snapshot);
        }

        private void OnOrderBookUpdateReceived(OrderBookUpdate update)
        {
            _orderBookService.ApplyOrderBookUpdate(update);
        }

        private void OnAggTradeReceived(AggTradeUpdate trade)
        {
            _orderBookService.AddTrade(trade);
        }

        private void OnMiniTickerReceived(MiniTickerUpdate ticker)
        {
            _orderBookService.UpdateLastTrade(ticker);
        }

        private void OnErrorOccurred(string error)
        {
            Dispatcher.Invoke(() =>
            {
                _logger.LogError($"WebSocket错误: {error}");
                // 不再显示弹窗，只记录日志，避免频繁弹窗干扰用户
            });
        }

        private void OnConnectionStatusChanged(bool isConnected)
        {
            Dispatcher.Invoke(() =>
            {
                // 更新UI状态指示器
                UpdateConnectionStatus(isConnected);
                _logger.LogInformation($"连接状态变化: {(isConnected ? "已连接" : "已断开")}");
            });
        }

        private async void OnResyncRequested(string symbol)
        {
            _logger.LogInformation($"开始重新同步订单薄: {symbol}");
            try
            {
                var snapshot = await _webSocketService.GetOrderBookSnapshotAsync(symbol);
                if (snapshot != null)
                {
                    _orderBookService.UpdateOrderBookSnapshot(snapshot);
                    _logger.LogInformation("订单薄重新同步完成");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重新同步订单薄时发生错误");
            }
        }

        private async void QueryButton_Click(object sender, RoutedEventArgs e)
        {
            var symbol = SymbolTextBox.Text?.Trim().ToUpper();
            if (!string.IsNullOrEmpty(symbol))
            {
                await ConnectToSymbolAsync(symbol);
                AddToHistory(symbol);
            }
        }

        private async void HistoryComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (HistoryComboBox.SelectedItem is string selectedSymbol)
            {
                SymbolTextBox.Text = selectedSymbol;
                await ConnectToSymbolAsync(selectedSymbol);
            }
        }

        private async System.Threading.Tasks.Task ConnectToSymbolAsync(string symbol)
        {
            try
            {
                _logger.LogInformation($"连接到交易对: {symbol}");
                _orderBookService.CurrentSymbol = symbol;
                await _webSocketService.ConnectAsync(symbol);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"连接到交易对 {symbol} 失败");
                MessageBox.Show($"连接失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddToHistory(string symbol)
        {
            if (!_symbolHistory.Contains(symbol))
            {
                _symbolHistory.Add(symbol);
                UpdateHistoryComboBox();
            }
        }

        private void UpdateHistoryComboBox()
        {
            HistoryComboBox.ItemsSource = _symbolHistory.ToList();
        }

        private void UpdateConnectionStatus(bool isConnected)
        {
            // 通过PriceInfoView更新连接状态
            var priceInfoView = FindName("PriceInfoView") as PriceInfoView;
            if (priceInfoView != null)
            {
                priceInfoView.UpdateConnectionStatus(isConnected);
            }

            // 更新按钮状态
            QueryButton.Background = isConnected ?
                new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(59, 130, 246)) :
                new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(239, 68, 68));
        }

        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // 应用程序启动时自动连接到BTCUSDT
            await ConnectToSymbolAsync("BTCUSDT");
        }

        protected override void OnClosed(EventArgs e)
        {
            _webSocketService?.Dispose();
            base.OnClosed(e);
        }
    }
}
