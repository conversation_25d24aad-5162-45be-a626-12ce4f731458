using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace BinanceOrderBookWPF
{
    public class PriceChangeToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double change)
            {
                if (change > 0)
                    return new SolidColorBrush(Color.FromRgb(46, 189, 133)); // 绿色 - 上涨 RGB(46,189,133)
                else if (change < 0)
                    return new SolidColorBrush(Color.FromRgb(246, 70, 93));  // 红色 - 下跌 RGB(246,70,93)
                else
                    return new SolidColorBrush(Colors.White); // 白色 - 无变化
            }
            return new SolidColorBrush(Colors.White);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class BuyerMakerToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isBuyerMaker)
            {
                // 如果是买方主动成交，显示红色（卖出）
                // 如果是卖方主动成交，显示绿色（买入）
                return isBuyerMaker
                    ? new SolidColorBrush(Color.FromRgb(246, 70, 93))  // 红色 RGB(246,70,93)
                    : new SolidColorBrush(Color.FromRgb(46, 189, 133)); // 绿色 RGB(46,189,133)
            }
            return new SolidColorBrush(Colors.White);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 布尔值到颜色转换器的别名，用于新的交易视图
    /// </summary>
    public class BoolToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isBuyerMaker)
            {
                // IsBuyerMaker = true 表示卖单（红色），false 表示买单（绿色）
                return isBuyerMaker ?
                    new SolidColorBrush(Color.FromRgb(246, 70, 93)) :   // 红色 - 卖单 RGB(246,70,93)
                    new SolidColorBrush(Color.FromRgb(46, 189, 133));   // 绿色 - 买单 RGB(46,189,133)
            }
            return new SolidColorBrush(Colors.White);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 数值精度格式化转换器
    /// </summary>
    public class PrecisionFormatConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double number)
            {
                // 根据数值大小自动调整精度
                if (number >= 1000)
                    return number.ToString("F2");
                else if (number >= 1)
                    return number.ToString("F4");
                else
                    return number.ToString("F6");
            }
            return value?.ToString() ?? string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 深度到宽度转换器，用于显示订单薄深度条
    /// </summary>
    public class DepthToWidthConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double depth)
            {
                // 将深度值转换为像素宽度，最大宽度为200像素
                var maxWidth = 200.0;
                var width = Math.Min(depth * maxWidth, maxWidth);
                return Math.Max(width, 0);
            }
            return 0.0;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }



    /// <summary>
    /// 延迟到颜色转换器，根据延迟高低显示不同颜色
    /// </summary>
    public class LatencyToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double latency)
            {
                if (latency < 50)
                    return new SolidColorBrush(Color.FromRgb(46, 189, 133)); // 绿色 - 低延迟
                else if (latency < 100)
                    return new SolidColorBrush(Color.FromRgb(255, 149, 0));  // 橙色 - 中等延迟
                else
                    return new SolidColorBrush(Color.FromRgb(246, 70, 93));  // 红色 - 高延迟
            }
            return new SolidColorBrush(Colors.White);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

}
