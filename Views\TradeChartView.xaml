<UserControl x:Class="BinanceOrderBookWPF.Views.TradeChartView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:BinanceOrderBookWPF.Views"
             xmlns:scottplot="clr-namespace:ScottPlot.WPF;assembly=ScottPlot.WPF"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="400"
             MinWidth="300" MinHeight="200"
             Background="#FF1E1E1E"
             Focusable="True"
             IsTabStop="True">
    
    <UserControl.Resources>
        <!-- Binance官方颜色 -->
        <SolidColorBrush x:Key="BuyBrush" Color="#0ECB81"/>
        <SolidColorBrush x:Key="SellBrush" Color="#F84960"/>
        <SolidColorBrush x:Key="BackgroundBrush" Color="#1E2329"/>
        <SolidColorBrush x:Key="BorderBrush" Color="#3C4043"/>
        <SolidColorBrush x:Key="TextBrush" Color="#EAECEF"/>
        <SolidColorBrush x:Key="GridLineBrush" Color="#2B3139"/>
        <SolidColorBrush x:Key="AxisBrush" Color="#848E9C"/>
        
        <!-- 图表标题样式 -->
        <Style x:Key="ChartTitleStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="BinancePlex, Microsoft YaHei"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="#EAECEF"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
        
        <!-- 轴标签样式 -->
        <Style x:Key="AxisLabelStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="BinancePlex, Microsoft YaHei"/>
            <Setter Property="FontSize" Value="10"/>
            <Setter Property="Foreground" Value="#848E9C"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
    </UserControl.Resources>
    
    <Grid Background="{StaticResource BackgroundBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Grid.Column="0"
                Background="#2B3139" BorderBrush="#3C4043"
                BorderThickness="0,0,0,1" Height="24">
            <Grid>
                <TextBlock Text="实时交易图表 (ScottPlot)"
                           Foreground="White"
                           FontSize="12"
                           VerticalAlignment="Center"
                           HorizontalAlignment="Center"/>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Center" Margin="0,0,10,0">
                    <TextBlock x:Name="TimeWindowDisplay"
                               Text="30分钟"
                               Foreground="#848E9C"
                               FontSize="10"
                               VerticalAlignment="Center"/>
                    <TextBlock Text=" | 内置缩放"
                               Foreground="#848E9C"
                               FontSize="9"
                               VerticalAlignment="Center"
                               Margin="5,0,0,0"/>
                </StackPanel>
            </Grid>
        </Border>
        


        <!-- 主图表区域 -->
        <Border Grid.Row="1" Grid.Column="0" Grid.RowSpan="2"
                Background="#1E1E1E"
                BorderBrush="#3C4043"
                BorderThickness="0,1,0,0">
            <scottplot:WpfPlot x:Name="TradeChart"
                              Background="#1E1E1E"/>
        </Border>

    </Grid>
</UserControl>
